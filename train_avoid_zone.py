#!/usr/bin/env python3
"""
Simple launcher script for avoid zone training
"""

import sys
import os

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Now import and run
from swarm_rl.train import main

if __name__ == '__main__':
    # Add avoid zone specific arguments if not provided
    if '--quads_mode=avoid_zone' not in sys.argv:
        sys.argv.append('--quads_mode=avoid_zone')
    
    # Add default avoid zone parameters if not provided
    default_args = [
        '--avoid_zone_num_zones=3',
        '--avoid_zone_radius=1.0', 
        '--avoid_zone_penalty=10.0',
        '--quads_num_agents=8',
        '--train_for_env_steps=50000000'
    ]
    
    for arg in default_args:
        param_name = arg.split('=')[0]
        if not any(a.startswith(param_name) for a in sys.argv):
            sys.argv.append(arg)
    
    print("Starting Avoid Zone training with arguments:")
    for arg in sys.argv[1:]:
        if arg.startswith('--'):
            print(f"  {arg}")
    
    sys.exit(main())
