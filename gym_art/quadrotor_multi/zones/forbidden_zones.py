import copy
import numpy as np
from numba import njit


class ForbiddenZones:
    """
    Manages forbidden zones (red circles) that drones must avoid.
    Similar to obstacles but with different visualization and behavior.
    """
    
    def __init__(self, zone_radius=1.0, quad_radius=0.046, num_zones=3):
        self.zone_radius = zone_radius
        self.quad_radius = quad_radius
        self.num_zones = num_zones
        self.zones = []  # List of zone positions [x, y, z]
        self.resolution = 0.1
        
        # Zone properties
        self.zone_height = 2.0  # Height of cylindrical forbidden zone
        self.penalty_strength = 10.0  # Penalty multiplier for entering zones
        
    def reset(self, room_dims, spawn_positions=None, goal_positions=None):
        """
        Reset and generate new forbidden zones.
        
        Args:
            room_dims: [width, height, depth] of the room
            spawn_positions: List of drone spawn positions to avoid
            goal_positions: List of goal positions to avoid
        """
        self.zones = []
        
        # Generate random zones while avoiding spawn and goal positions
        for _ in range(self.num_zones):
            zone_pos = self._generate_safe_zone_position(
                room_dims, spawn_positions, goal_positions
            )
            self.zones.append(zone_pos)
            
        return np.array(self.zones)
    
    def _generate_safe_zone_position(self, room_dims, spawn_positions=None, goal_positions=None):
        """Generate a zone position that doesn't interfere with spawn/goal positions"""
        max_attempts = 100
        min_distance_from_important = 2.0  # Minimum distance from spawn/goal
        
        for _ in range(max_attempts):
            # Generate random position within room bounds
            x = np.random.uniform(-room_dims[0]/2 + self.zone_radius, 
                                room_dims[0]/2 - self.zone_radius)
            y = np.random.uniform(-room_dims[1]/2 + self.zone_radius, 
                                room_dims[1]/2 - self.zone_radius)
            z = np.random.uniform(0.5, room_dims[2] - 0.5)
            
            zone_pos = np.array([x, y, z])
            
            # Check distance from spawn positions
            safe_from_spawn = True
            if spawn_positions is not None:
                for spawn_pos in spawn_positions:
                    if np.linalg.norm(zone_pos[:2] - spawn_pos[:2]) < min_distance_from_important:
                        safe_from_spawn = False
                        break
            
            # Check distance from goal positions
            safe_from_goals = True
            if goal_positions is not None:
                for goal_pos in goal_positions:
                    if np.linalg.norm(zone_pos[:2] - goal_pos[:2]) < min_distance_from_important:
                        safe_from_goals = False
                        break
            
            if safe_from_spawn and safe_from_goals:
                return zone_pos
        
        # Fallback: return a position even if not ideal
        return np.array([
            np.random.uniform(-room_dims[0]/4, room_dims[0]/4),
            np.random.uniform(-room_dims[1]/4, room_dims[1]/4),
            np.random.uniform(1.0, room_dims[2] - 1.0)
        ])
    
    def get_zone_violations(self, quad_positions):
        """
        Check which drones are violating forbidden zones.
        
        Args:
            quad_positions: Array of drone positions [N, 3]
            
        Returns:
            violations: Array of violation flags [N] (True if violating)
            penalties: Array of penalty values [N]
        """
        if len(self.zones) == 0:
            return np.zeros(len(quad_positions), dtype=bool), np.zeros(len(quad_positions))
        
        violations = np.zeros(len(quad_positions), dtype=bool)
        penalties = np.zeros(len(quad_positions))
        
        for i, quad_pos in enumerate(quad_positions):
            for zone_pos in self.zones:
                # Check 2D distance (zones are cylindrical)
                distance_2d = np.linalg.norm(quad_pos[:2] - zone_pos[:2])
                
                # Check if within zone radius and height
                within_radius = distance_2d <= (self.zone_radius + self.quad_radius)
                within_height = (zone_pos[2] - self.zone_height/2) <= quad_pos[2] <= (zone_pos[2] + self.zone_height/2)
                
                if within_radius and within_height:
                    violations[i] = True
                    # Penalty increases as drone gets closer to center
                    normalized_distance = distance_2d / self.zone_radius
                    penalties[i] = max(penalties[i], self.penalty_strength * (1.0 - normalized_distance))
                    break  # One violation per drone is enough
        
        return violations, penalties
    
    def get_zone_sdf_observations(self, quad_positions):
        """
        Get signed distance field observations around each drone for forbidden zones.
        Similar to obstacle SDF but for zones.
        
        Args:
            quad_positions: Array of drone positions [N, 3]
            
        Returns:
            sdf_obs: Array of SDF values [N, 9] (3x3 grid around each drone)
        """
        if len(self.zones) == 0:
            return 100 * np.ones((len(quad_positions), 9))
        
        zones_2d = np.array([zone[:2] for zone in self.zones])
        quads_2d = quad_positions[:, :2]
        
        sdf_obs = get_zone_surround_sdfs(
            quad_poses=quads_2d,
            zone_poses=zones_2d,
            zone_radius=self.zone_radius,
            resolution=self.resolution
        )
        
        return sdf_obs
    
    def update_zones(self, new_positions=None):
        """
        Update zone positions (for dynamic zones in future).
        Currently zones are static.
        """
        if new_positions is not None:
            self.zones = copy.deepcopy(new_positions)


@njit
def get_zone_surround_sdfs(quad_poses, zone_poses, zone_radius, resolution=0.1):
    """
    Numba-optimized function to get SDF values around each drone for forbidden zones.
    """
    num_quads = len(quad_poses)
    sdf_obs = 100.0 * np.ones((num_quads, 9))
    
    # 3x3 grid offsets
    offsets = np.array([
        [-resolution, -resolution],  # 0: bottom-left
        [0, -resolution],            # 1: bottom-center  
        [resolution, -resolution],   # 2: bottom-right
        [-resolution, 0],            # 3: middle-left
        [0, 0],                      # 4: center
        [resolution, 0],             # 5: middle-right
        [-resolution, resolution],   # 6: top-left
        [0, resolution],             # 7: top-center
        [resolution, resolution]     # 8: top-right
    ])
    
    for i, quad_pos in enumerate(quad_poses):
        for grid_idx in range(9):
            grid_pos = quad_pos + offsets[grid_idx]
            
            # Find minimum distance to any zone
            min_dist = 100.0
            for zone_pos in zone_poses:
                dist = np.linalg.norm(grid_pos - zone_pos)
                if dist < min_dist:
                    min_dist = dist
            
            # SDF: negative inside zone, positive outside
            sdf_obs[i, grid_idx] = min_dist - zone_radius
    
    return sdf_obs
