import numpy as np
import copy

from gym_art.quadrotor_multi.scenarios.base import QuadrotorScenario
from gym_art.quadrotor_multi.zones.forbidden_zones import ForbiddenZones


class Scenario_avoid_zone(QuadrotorScenario):
    """
    Avoid the Zone Scenario
    
    Drones must navigate to their goals while avoiding randomly placed forbidden zones (red circles).
    The zones are cylindrical and provide negative rewards when entered.
    """
    
    def __init__(self, quads_mode, envs, num_agents, room_dims, avoid_zone_params=None):
        super().__init__(quads_mode, envs, num_agents, room_dims, avoid_zone_params)

        # Scenario parameters (use provided params or defaults)
        params = avoid_zone_params or {}
        self.zone_radius = params.get('radius', 1.0)
        self.num_zones = params.get('num_zones', max(2, num_agents // 3))
        self.goal_change_interval = params.get('goal_change_interval', 8.0)
        self.zone_change_interval = params.get('zone_change_interval', 15.0)
        self.zone_penalty = params.get('penalty', 10.0)
        
        # Initialize forbidden zones system
        self.forbidden_zones = ForbiddenZones(
            zone_radius=self.zone_radius,
            quad_radius=0.046,  # Standard quad radius
            num_zones=self.num_zones
        )
        self.forbidden_zones.penalty_strength = self.zone_penalty
        
        # Timing control
        self.control_freq = self.envs[0].control_freq if self.envs else 50
        self.goal_change_steps = int(self.goal_change_interval * self.control_freq)
        self.zone_change_steps = int(self.zone_change_interval * self.control_freq)
        
        # State tracking
        self.current_step = 0
        self.last_goal_change = 0
        self.last_zone_change = 0
        
        # Goals and spawn positions
        self.spawn_positions = None
        self.current_goals = None
        
        # Performance metrics
        self.zone_violations_count = 0
        self.total_penalty = 0.0
        
    def reset(self):
        """Reset the scenario - called at the beginning of each episode"""
        self.current_step = 0
        self.last_goal_change = 0
        self.last_zone_change = 0
        self.zone_violations_count = 0
        self.total_penalty = 0.0
        
        # Generate initial spawn positions in a circle formation
        self.spawn_positions = self.generate_goals(
            num_agents=self.num_agents,
            formation_center=np.array([-3.0, 0.0, 2.0]),
            layer_dist=0.0
        )
        
        # Generate initial goal positions
        self.current_goals = self.generate_goals(
            num_agents=self.num_agents,
            formation_center=np.array([3.0, 0.0, 2.0]),
            layer_dist=0.0
        )
        
        # Initialize forbidden zones (avoid spawn and goal areas)
        self.forbidden_zones.reset(
            room_dims=self.room_dims,
            spawn_positions=self.spawn_positions,
            goal_positions=self.current_goals
        )
        
        # Set initial goals for all environments
        for i, env in enumerate(self.envs):
            env.goal = self.current_goals[i]
            
        return self.forbidden_zones.zones
    
    def step(self):
        """Step the scenario - called every simulation step"""
        self.current_step += 1
        
        # Check if it's time to change goals
        if (self.current_step - self.last_goal_change) >= self.goal_change_steps:
            self._change_goals()
            self.last_goal_change = self.current_step
        
        # Check if it's time to change zones (less frequent)
        if (self.current_step - self.last_zone_change) >= self.zone_change_steps:
            self._change_zones()
            self.last_zone_change = self.current_step
    
    def _change_goals(self):
        """Generate new goal positions for all drones"""
        # Generate new goals in different areas of the room
        goal_areas = [
            np.array([3.0, 3.0, 2.0]),   # Top-right
            np.array([3.0, -3.0, 2.0]),  # Bottom-right
            np.array([-3.0, 3.0, 2.0]),  # Top-left
            np.array([-3.0, -3.0, 2.0]), # Bottom-left
            np.array([0.0, 3.0, 3.0]),   # Top-center-high
            np.array([0.0, -3.0, 1.0]),  # Bottom-center-low
        ]
        
        # Choose a random goal area
        goal_center = goal_areas[np.random.randint(len(goal_areas))]
        
        # Generate new goals
        self.current_goals = self.generate_goals(
            num_agents=self.num_agents,
            formation_center=goal_center,
            layer_dist=0.3  # Small spread around the center
        )
        
        # Update environment goals
        for i, env in enumerate(self.envs):
            env.goal = self.current_goals[i]
    
    def _change_zones(self):
        """Generate new forbidden zone positions"""
        # Get current drone positions to avoid placing zones on them
        current_positions = []
        for env in self.envs:
            if hasattr(env, 'dynamics'):
                current_positions.append(env.dynamics.pos)
        
        # Reset zones with new positions
        self.forbidden_zones.reset(
            room_dims=self.room_dims,
            spawn_positions=current_positions,
            goal_positions=self.current_goals
        )
    
    def get_zone_rewards(self, quad_positions):
        """
        Calculate rewards/penalties based on zone violations.
        
        Args:
            quad_positions: Array of current drone positions [N, 3]
            
        Returns:
            rewards: Array of reward values [N] (negative for violations)
        """
        violations, penalties = self.forbidden_zones.get_zone_violations(quad_positions)
        
        # Track violations for statistics
        self.zone_violations_count += np.sum(violations)
        self.total_penalty += np.sum(penalties)
        
        # Convert penalties to negative rewards
        rewards = -penalties
        
        return rewards, violations
    
    def get_zone_observations(self, quad_positions):
        """
        Get zone-related observations for the drones.
        
        Args:
            quad_positions: Array of current drone positions [N, 3]
            
        Returns:
            zone_obs: Array of zone SDF observations [N, 9]
        """
        return self.forbidden_zones.get_zone_sdf_observations(quad_positions)
    
    def get_zones_for_visualization(self):
        """
        Get zone data for visualization.
        
        Returns:
            zones: List of zone dictionaries with position and radius
        """
        zones_data = []
        for zone_pos in self.forbidden_zones.zones:
            zones_data.append({
                'position': {
                    'x': float(zone_pos[0]),
                    'y': float(zone_pos[1]), 
                    'z': float(zone_pos[2])
                },
                'radius': float(self.forbidden_zones.zone_radius),
                'height': float(self.forbidden_zones.zone_height),
                'color': 'red',
                'type': 'forbidden_zone'
            })
        return zones_data
    
    def get_scenario_info(self):
        """Get scenario-specific information for logging"""
        return {
            'scenario_name': 'avoid_zone',
            'num_zones': self.num_zones,
            'zone_radius': self.zone_radius,
            'zone_violations': self.zone_violations_count,
            'total_penalty': self.total_penalty,
            'current_step': self.current_step
        }
    
    def name(self):
        """Return scenario name for identification"""
        return 'avoid_zone'
