#!/bin/bash

# Avoid Zone Training Script
# This script trains the quadrotor swarm with the avoid_zone scenario

# Default values
DEFAULT_NUM_ZONES="3"
DEFAULT_ZONE_RADIUS="1.0"
DEFAULT_ZONE_PENALTY="10.0"
DEFAULT_NUM_AGENTS="8"
DEFAULT_TRAIN_STEPS="50000000"

# Parse command line arguments or use defaults
NUM_ZONES="${1:-$DEFAULT_NUM_ZONES}"
ZONE_RADIUS="${2:-$DEFAULT_ZONE_RADIUS}"
ZONE_PENALTY="${3:-$DEFAULT_ZONE_PENALTY}"
NUM_AGENTS="${4:-$DEFAULT_NUM_AGENTS}"
TRAIN_STEPS="${5:-$DEFAULT_TRAIN_STEPS}"

# Set the script directory as the working directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Function to show usage
show_usage() {
    echo "Usage: $0 [NUM_ZONES] [ZONE_RADIUS] [ZONE_PENALTY] [NUM_AGENTS] [TRAIN_STEPS]"
    echo ""
    echo "Default values:"
    echo "  NUM_ZONES: $DEFAULT_NUM_ZONES"
    echo "  ZONE_RADIUS: $DEFAULT_ZONE_RADIUS"
    echo "  ZONE_PENALTY: $DEFAULT_ZONE_PENALTY"
    echo "  NUM_AGENTS: $DEFAULT_NUM_AGENTS"
    echo "  TRAIN_STEPS: $DEFAULT_TRAIN_STEPS"
    echo ""
    echo "Examples:"
    echo "  $0                           # Use all defaults"
    echo "  $0 4 1.2 15.0               # 4 zones, radius 1.2, penalty 15.0"
    echo "  $0 3 1.0 10.0 12 100000000   # Custom all parameters"
    echo ""
    echo "This will train the avoid_zone scenario where drones must avoid randomly"
    echo "appearing forbidden red zones while navigating to their targets."
}

# Show help if requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "Error: Python is not installed or not in PATH"
    exit 1
fi

# Set PYTHONPATH to include current directory
export PYTHONPATH=$PYTHONPATH:$(pwd)

# Debug: Show current setup
echo "Debug info:"
echo "  Current directory: $(pwd)"
echo "  PYTHONPATH: $PYTHONPATH"
echo "  Python version: $(python --version)"

# Check if the swarm_rl module exists
if ! python -c "import swarm_rl.train" &> /dev/null; then
    echo "Error: swarm_rl module not found."
    echo "Trying alternative import methods..."

    # Try direct execution
    if [ -f "swarm_rl/train.py" ]; then
        echo "Found swarm_rl/train.py, will use direct execution"
        TRAIN_COMMAND="python swarm_rl/train.py"
    else
        echo "swarm_rl/train.py not found either"
        exit 1
    fi
else
    echo "swarm_rl module found successfully"
    TRAIN_COMMAND="python -m swarm_rl.train"
fi

# Create experiment name with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M")
EXPERIMENT_NAME="avoid_zone_${NUM_ZONES}zones_r${ZONE_RADIUS}_p${ZONE_PENALTY}_${TIMESTAMP}"

echo "Starting Avoid Zone training..."
echo "Working directory: $SCRIPT_DIR"
echo "Experiment name: $EXPERIMENT_NAME"
echo "Configuration:"
echo "  Number of zones: $NUM_ZONES"
echo "  Zone radius: $ZONE_RADIUS"
echo "  Zone penalty: $ZONE_PENALTY"
echo "  Number of agents: $NUM_AGENTS"
echo "  Training steps: $TRAIN_STEPS"
echo ""

# Run the training command
$TRAIN_COMMAND \
    --algo=APPO \
    --env=quadrotor_multi \
    --experiment="$EXPERIMENT_NAME" \
    --quads_mode=avoid_zone \
    --avoid_zone_num_zones="$NUM_ZONES" \
    --avoid_zone_radius="$ZONE_RADIUS" \
    --avoid_zone_penalty="$ZONE_PENALTY" \
    --avoid_zone_goal_change_interval=8.0 \
    --avoid_zone_zone_change_interval=15.0 \
    --quads_num_agents="$NUM_AGENTS" \
    --quads_episode_duration=15.0 \
    --quads_room_dims 10.0 10.0 10.0 \
    --quads_obs_repr=xyz_vxyz_R_omega \
    --quads_neighbor_visible_num=2 \
    --quads_neighbor_obs_type=pos_vel \
    --quads_encoder_type=attention \
    --train_for_env_steps="$TRAIN_STEPS" \
    --num_workers=8 \
    --num_envs_per_worker=4 \
    --learning_rate=0.0003 \
    --gamma=0.99 \
    --use_rnn=False \
    --ppo_clip_value=0.2 \
    --recurrence=1 \
    --nonlinearity=tanh \
    --actor_critic_share_weights=True \
    --policy_initialization=xavier_uniform \
    --adaptive_stddev=False \
    --with_vtrace=False \
    --max_policy_lag=100000000 \
    --gae_lambda=0.95 \
    --max_grad_norm=5.0 \
    --exploration_loss_coeff=0.0 \
    --rollout=128 \
    --batch_size=1024 \
    --quads_collision_reward=-0.1 \
    --quads_collision_smooth_max_penalty=10.0 \
    --quads_render=False \
    --experiment_summaries_interval=10 \
    --save_every_sec=120 \
    --stats_avg=100

echo ""
echo "Training completed!"
echo "Model saved in: ./train_dir/$EXPERIMENT_NAME"
echo ""
echo "To run inference with web visualization, use:"
echo "  ./web_inference_avoid_zone.sh $EXPERIMENT_NAME"
