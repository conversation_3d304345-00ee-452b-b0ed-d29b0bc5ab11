#!/bin/bash

# Avoid Zone Web Visualization Script (Inference)
# This script runs the quadrotor swarm avoid_zone scenario with WebSocket support

# Default values
DEFAULT_ALGO="APPO"
DEFAULT_ENV="quadrotor_multi_websocket"
DEFAULT_TRAIN_DIR=""  # Will be auto-detected
DEFAULT_EXPERIMENT=""  # Will be auto-detected
DEFAULT_WEBSOCKET_PORT="8765"
DEFAULT_NUM_ZONES="3"
DEFAULT_ZONE_RADIUS="1.0"
DEFAULT_ZONE_PENALTY="10.0"
DEFAULT_NUM_AGENTS="8"

# Parse command line arguments or use defaults
EXPERIMENT="${1:-$DEFAULT_EXPERIMENT}"
ALGO="${2:-$DEFAULT_ALGO}"
ENV="${3:-$DEFAULT_ENV}"
WEBSOCKET_PORT="${4:-$DEFAULT_WEBSOCKET_PORT}"
NUM_ZONES="${5:-$DEFAULT_NUM_ZONES}"
ZONE_RADIUS="${6:-$DEFAULT_ZONE_RADIUS}"
ZONE_PENALTY="${7:-$DEFAULT_ZONE_PENALTY}"
NUM_AGENTS="${8:-$DEFAULT_NUM_AGENTS}"

# Set the script directory as the working directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Function to show usage
show_usage() {
    echo "Usage: $0 [EXPERIMENT] [ALGO] [ENV] [WEBSOCKET_PORT] [NUM_ZONES] [ZONE_RADIUS] [ZONE_PENALTY] [NUM_AGENTS]"
    echo ""
    echo "Default values:"
    echo "  EXPERIMENT: Auto-detect latest avoid_zone experiment"
    echo "  ALGO: $DEFAULT_ALGO"
    echo "  ENV: $DEFAULT_ENV"
    echo "  WEBSOCKET_PORT: $DEFAULT_WEBSOCKET_PORT"
    echo "  NUM_ZONES: $DEFAULT_NUM_ZONES"
    echo "  ZONE_RADIUS: $DEFAULT_ZONE_RADIUS"
    echo "  ZONE_PENALTY: $DEFAULT_ZONE_PENALTY"
    echo "  NUM_AGENTS: $DEFAULT_NUM_AGENTS"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Auto-detect latest experiment"
    echo "  $0 avoid_zone_20250101_1200          # Use specific experiment"
    echo "  $0 my_experiment APPO quadrotor_multi_websocket 8080 4 1.2 15.0 12"
    echo ""
    echo "This will run inference for the avoid_zone scenario with web visualization."
    echo "Open http://localhost:8080 in your browser to see the visualization."
    echo "Make sure to start the web server first: python swarm_rl/web/server.py"
}

# Show help if requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# Auto-detect experiment if not provided
if [ -z "$EXPERIMENT" ]; then
    echo "Auto-detecting latest avoid_zone experiment..."
    if [ -d "./train_dir" ]; then
        # Find the latest avoid_zone experiment
        LATEST_EXPERIMENT=$(ls -t ./train_dir/ | grep "avoid_zone" | head -n 1)
        if [ -n "$LATEST_EXPERIMENT" ]; then
            EXPERIMENT="$LATEST_EXPERIMENT"
            echo "Found: $EXPERIMENT"
        else
            echo "No avoid_zone experiments found in ./train_dir/"
            echo "Available experiments:"
            ls -la ./train_dir/ 2>/dev/null || echo "train_dir directory not found"
            echo ""
            echo "Please specify an experiment name or train a model first:"
            echo "  ./train_avoid_zone.sh"
            exit 1
        fi
    else
        echo "Error: train_dir not found"
        exit 1
    fi
fi

# Set train directory
TRAIN_DIR="./train_dir/$EXPERIMENT"

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "Error: Python is not installed or not in PATH"
    exit 1
fi

# Set PYTHONPATH to include current directory
export PYTHONPATH=$PYTHONPATH:$(pwd)

# Check if the swarm_rl module exists
if ! python -c "import swarm_rl.web.enjoy" &> /dev/null; then
    echo "Error: swarm_rl module not found. Make sure you're in the correct directory."
    echo "Current directory: $(pwd)"
    echo "Try running: export PYTHONPATH=\$PYTHONPATH:\$(pwd)"
    exit 1
fi

# Check if train directory exists
if [ ! -d "$TRAIN_DIR" ]; then
    echo "Error: Training directory '$TRAIN_DIR' not found"
    echo "Available experiments in train_dir:"
    ls -la ./train_dir/ 2>/dev/null || echo "train_dir directory not found"
    echo ""
    echo "Use --help to see usage information"
    exit 1
fi

# Check if web server is running
if ! curl -s http://localhost:8080 > /dev/null 2>&1; then
    echo "Warning: Web server doesn't seem to be running on port 8080"
    echo "Start the web server in another terminal:"
    echo "  python swarm_rl/web/server.py"
    echo ""
    echo "Continuing with inference..."
fi

# Run the visualization command
echo "Starting Avoid Zone inference with web visualization..."
echo "Working directory: $SCRIPT_DIR"
echo "Algorithm: $ALGO"
echo "Environment: $ENV"
echo "Training directory: $TRAIN_DIR"
echo "Experiment: $EXPERIMENT"
echo "WebSocket server will be available at: ws://localhost:$WEBSOCKET_PORT"
echo "Web visualization: http://localhost:8080"
echo ""
echo "Avoid Zone Configuration:"
echo "  Number of zones: $NUM_ZONES"
echo "  Zone radius: $ZONE_RADIUS"
echo "  Zone penalty: $ZONE_PENALTY"
echo "  Number of agents: $NUM_AGENTS"
echo ""

python -m swarm_rl.web.enjoy \
    --algo="$ALGO" \
    --env="$ENV" \
    --train_dir="$TRAIN_DIR" \
    --experiment="$EXPERIMENT" \
    --websocket_port="$WEBSOCKET_PORT" \
    --quads_mode=avoid_zone \
    --avoid_zone_num_zones="$NUM_ZONES" \
    --avoid_zone_radius="$ZONE_RADIUS" \
    --avoid_zone_penalty="$ZONE_PENALTY" \
    --avoid_zone_goal_change_interval=8.0 \
    --avoid_zone_zone_change_interval=15.0 \
    --quads_num_agents="$NUM_AGENTS" \
    --quads_episode_duration=15.0 \
    --quads_room_dims 10.0 10.0 10.0 \
    --quads_obs_repr=xyz_vxyz_R_omega \
    --quads_neighbor_visible_num=2 \
    --quads_neighbor_obs_type=pos_vel \
    --quads_encoder_type=attention \
    --encoder_type=mlp \
    --encoder_subtype=mlp_quads \
    --hidden_size=256 \
    --no_render

echo ""
echo "Inference ended."
echo ""
echo "If you saw red cylindrical zones in the web visualization, the avoid_zone scenario is working correctly!"
